# 更新日志

所有重要的项目变更都会记录在这个文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [1.2.0] - 2024-12-XX

### 新增
- ✨ 硅基流动(SiliconFlow) AI服务支持
- 🔧 自定义AI模型选择功能
- 📊 高级AI参数配置（温度、Top-P、超时等）
- 📝 自定义提示词模板系统
- 🎨 折叠式高级配置界面
- 🔍 智能API地址修正功能
- 🔐 智能Token格式处理

### 改进
- 🎨 重新设计配置页面UI，提升用户体验
- 🔧 增强API连接测试功能，支持多种格式尝试
- 📱 优化响应式设计，适配不同屏幕尺寸
- 🚀 提升AI总结质量和速度
- 📋 改进错误提示和用户反馈

### 修复
- 🐛 修复API地址单复数问题
- 🔧 修复Token格式兼容性问题
- 🎯 修复自定义模型选择逻辑
- 📝 修复配置保存和加载问题

### 技术改进
- 🏗️ 重构配置管理系统
- 🔒 增强错误处理和异常捕获
- 📊 添加详细的调试信息输出
- ⚡ 优化性能和内存使用

## [1.1.0] - 2024-12-XX

### 新增
- 🤖 AI文章总结功能
- 🧠 智能内容分类系统
- ⌨️ 快捷键支持
- 🏷️ 自动标签生成
- 📊 内容质量评分
- 🔍 关键词自动提取

### 改进
- 🎨 全新的用户界面设计
- 📱 响应式布局优化
- 🚀 提升收集速度和稳定性
- 📝 增强内容格式化

### 修复
- 🐛 修复页面收集失败问题
- 🔧 修复划词收集兼容性
- 📋 修复配置同步问题

## [1.0.0] - 2024-12-XX

### 新增
- 🎉 项目首次发布
- 📝 基础网页内容收集功能
- 🔗 Blinko API集成
- ✂️ 划词收集功能
- 📌 一键页面保存
- ⚙️ 基础配置管理
- 🎯 右键菜单集成

### 技术特性
- 🏗️ Chrome扩展架构
- 🔒 安全的本地存储
- 📡 异步API调用
- 🎨 现代化UI设计

---

## 版本说明

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

## 图标说明

- ✨ 新增功能
- 🔧 功能改进
- 🐛 Bug修复
- 🎨 UI/UX改进
- 📝 文档更新
- 🚀 性能优化
- 🔒 安全性改进
- 🏗️ 架构变更
- 📊 数据/分析相关
- ⚡ 性能提升