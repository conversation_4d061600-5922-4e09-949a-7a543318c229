# 🚀 Blinko智能收集器

一个功能强大的Chrome扩展，帮助你智能收集和整理网页内容到Blinko笔记系统。

## ✨ 主要功能

### 🤖 AI驱动的智能收集
- **AI文章总结**：使用多种AI服务（OpenAI、Claude、DeepSeek、通义千问、硅基流动）自动总结文章核心要点
- **智能分类**：基于内容自动分类并添加相关标签
- **多模型支持**：支持自定义AI模型，灵活配置参数

### 📝 多种收集方式
- **一键收集**：快速保存当前页面到Blinko
- **划词收集**：选中文本即可收集，支持智能高亮反馈
- **智能分析**：深度分析页面内容，提取关键信息和关键词

### ⌨️ 便捷操作
- **快捷键支持**：
  - `Ctrl+Shift+S` - 快速收集当前页面
  - `Ctrl+Shift+A` - AI总结当前文章
  - `Ctrl+Shift+C` - 收集选中文本
  - `Ctrl+Shift+O` - 打开配置页面
- **右键菜单**：集成到浏览器右键菜单，操作更便捷
- **实时反馈**：页面通知和状态提示，操作结果一目了然

### 🧠 智能特性
- **内容去重**：自动检测重复内容
- **关键词提取**：智能提取文章关键词
- **域名标签**：自动添加来源网站标签
- **时间标签**：记录收集时间，便于后续整理

## 🛠️ 支持的AI服务

| 服务商 | 模型支持 | 特点 |
|--------|----------|------|
| OpenAI | GPT-3.5/4/4o系列 | 业界领先，质量稳定 |
| Claude | Claude 3系列 | 长文本处理能力强 |
| DeepSeek | DeepSeek Chat/Coder | 国产优秀，性价比高 |
| 通义千问 | Qwen系列 | 阿里云服务，中文优化 |
| 硅基流动 | 多种开源模型 | 国内访问快，价格优惠 |
| 自定义 | 兼容OpenAI API | 支持任何兼容服务 |

## 📦 安装方法

### 方法一：Chrome Web Store（推荐）
*即将上架Chrome Web Store*

### 方法二：开发者模式安装
1. 下载项目源码
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 扩展安装完成

## ⚙️ 配置说明

### Blinko API配置
1. 获取你的Blinko服务地址和Token
2. 在扩展配置页面填入相关信息
3. 点击"测试连接"确保配置正确

### AI服务配置
1. 选择你偏好的AI服务商
2. 填入对应的API密钥
3. 根据需要调整模型和参数
4. 测试连接确保服务可用

### 智能分类配置
- 启用/禁用自动分类功能
- 调整分类置信度阈值
- 自定义标签规则

## 🎯 使用场景

### 📚 学习研究
- 收集学术论文和技术文档
- AI总结帮助快速理解核心内容
- 智能分类便于知识管理

### 💼 工作效率
- 收集行业资讯和竞品分析
- 快速整理会议资料和项目文档
- 团队知识库建设

### 🌐 信息整理
- 收集感兴趣的文章和博客
- 整理购物清单和旅行攻略
- 个人知识库构建

## 🔧 技术特性

### 架构设计
- **模块化设计**：清晰的代码结构，易于维护和扩展
- **异步处理**：非阻塞操作，不影响浏览体验
- **错误处理**：完善的异常处理和用户反馈

### 安全性
- **本地存储**：敏感信息本地加密存储
- **权限最小化**：只请求必要的浏览器权限
- **数据保护**：不收集用户隐私数据

### 兼容性
- **Chrome 88+**：支持主流Chrome版本
- **跨平台**：Windows、macOS、Linux全平台支持
- **响应式设计**：适配不同屏幕尺寸

## 🚀 更新日志

### v1.2.0 (2024-12-XX)
- ✨ 新增硅基流动AI服务支持
- 🔧 完善自定义模型选择功能
- 🎨 优化配置页面用户体验
- 🐛 修复API连接问题
- 📝 增强智能分类算法

### v1.1.0 (2024-12-XX)
- ✨ 新增AI总结功能
- 🔧 添加智能分类系统
- ⌨️ 支持快捷键操作
- 🎨 重新设计用户界面

### v1.0.0 (2024-12-XX)
- 🎉 首次发布
- 📝 基础收集功能
- 🔗 Blinko API集成

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境
1. 克隆项目：`git clone https://github.com/your-username/blinko-smart-collector.git`
2. 安装依赖：项目为纯前端，无需额外依赖
3. 加载扩展：在Chrome中加载开发版本

### 提交规范
- 使用清晰的commit信息
- 遵循现有代码风格
- 添加必要的注释和文档

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [Blinko](https://github.com/blinko-space/blinko) - 优秀的笔记系统
- 各大AI服务商提供的强大API支持
- 开源社区的贡献和反馈

## 📞 联系方式

- 项目主页：[GitHub Repository](https://github.com/your-username/blinko-smart-collector)
- 问题反馈：[Issues](https://github.com/your-username/blinko-smart-collector/issues)
- 功能建议：[Discussions](https://github.com/your-username/blinko-smart-collector/discussions)

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！