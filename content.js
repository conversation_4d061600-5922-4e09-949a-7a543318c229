// 页面内容脚本 - 处理页面级交互和快捷键反馈
(function() {
  'use strict';

  console.log('🚀 Blinko智能收集器 Content Script 已加载');

  // 检查当前页面是否支持插件功能
  function isSupportedPage() {
    const url = window.location.href;
    const unsupportedProtocols = ['chrome:', 'chrome-extension:', 'moz-extension:', 'edge:', 'about:'];
    const isSupported = !unsupportedProtocols.some(protocol => url.startsWith(protocol));

    if (!isSupported) {
      console.log('⚠️ 当前页面不支持Blinko插件功能:', url);
    } else {
      console.log('✅ 当前页面支持Blinko插件功能:', url);
    }

    return isSupported;
  }

  // 页面支持检查
  const pageSupported = isSupportedPage();

  // 监听来自background的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    switch (request.action) {
      case 'getSelectedText':
        sendResponse({ text: window.getSelection().toString() });
        break;
      case 'showPageNotification':
        showPageNotification(request.message, request.type);
        break;
      case 'highlightCollectedText':
        highlightCollectedText();
        break;
      case 'toggleSidebar':
      case 'toggleDrawer':
        toggleDrawer();
        break;
      case 'showDrawer':
        showDrawer();
        break;
      case 'hideDrawer':
        hideDrawer();
        break;
    }
  });
  
  // 在页面上显示临时通知
  function showPageNotification(message, type = 'success') {
    // 移除现有通知
    const existingNotification = document.querySelector('.blinko-page-notification');
    if (existingNotification) {
      existingNotification.remove();
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'blinko-page-notification';
    notification.textContent = message;
    
    // 样式
    const styles = {
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#fff3cd',
      color: type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#856404',
      padding: '12px 20px',
      borderRadius: '8px',
      border: `1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#ffeaa7'}`,
      fontSize: '14px',
      fontWeight: '500',
      zIndex: '10000',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      maxWidth: '300px',
      wordWrap: 'break-word',
      animation: 'blinkoSlideIn 0.3s ease-out'
    };
    
    Object.assign(notification.style, styles);
    
    // 添加动画样式
    if (!document.querySelector('#blinko-notification-styles')) {
      const styleSheet = document.createElement('style');
      styleSheet.id = 'blinko-notification-styles';
      styleSheet.textContent = `
        @keyframes blinkoSlideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes blinkoSlideOut {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(styleSheet);
    }
    
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
      notification.style.animation = 'blinkoSlideOut 0.3s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }, 3000);
    
    // 点击关闭
    notification.addEventListener('click', () => {
      notification.style.animation = 'blinkoSlideOut 0.3s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    });
  }
  
  // 高亮显示刚收集的文本
  function highlightCollectedText() {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const span = document.createElement('span');
      span.className = 'blinko-collected-highlight';
      span.style.cssText = `
        background: linear-gradient(90deg, #ffeb3b, #ffc107);
        padding: 2px 4px;
        border-radius: 3px;
        animation: blinkoHighlight 2s ease-out;
      `;
      
      // 添加高亮动画
      if (!document.querySelector('#blinko-highlight-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'blinko-highlight-styles';
        styleSheet.textContent = `
          @keyframes blinkoHighlight {
            0% { background: #ffeb3b; transform: scale(1.05); }
            50% { background: #ffc107; }
            100% { background: transparent; transform: scale(1); }
          }
        `;
        document.head.appendChild(styleSheet);
      }
      
      try {
        range.surroundContents(span);
        
        // 2秒后移除高亮
        setTimeout(() => {
          const parent = span.parentNode;
          if (parent) {
            parent.replaceChild(document.createTextNode(span.textContent), span);
            parent.normalize();
          }
        }, 2000);
      } catch (e) {
        // 如果无法包围内容，则跳过高亮
      }
    }
  }
  
  // 监听键盘事件，用于快捷键反馈
  document.addEventListener('keydown', function(e) {
    // 检查是否是Blinko快捷键
    const isCtrlOrCmd = e.ctrlKey || e.metaKey;
    const isShift = e.shiftKey;
    
    if (isCtrlOrCmd && isShift) {
      switch (e.key.toLowerCase()) {
        case 's':
          e.preventDefault();
          showPageNotification('🚀 正在收集当前页面...', 'info');
          break;
        case 'a':
          e.preventDefault();
          showPageNotification('🤖 正在AI总结文章...', 'info');
          break;
        case 'c':
          e.preventDefault();
          const selectedText = window.getSelection().toString();
          if (selectedText) {
            showPageNotification('✂️ 正在收集选中文本...', 'info');
            highlightCollectedText();
          } else {
            showPageNotification('❌ 请先选中要收集的文本', 'error');
          }
          break;
      }
    }
  });
  
  // 监听选择变化，为快捷键收集做准备
  let lastSelection = '';
  document.addEventListener('selectionchange', function() {
    const currentSelection = window.getSelection().toString();
    if (currentSelection !== lastSelection) {
      lastSelection = currentSelection;
      // 可以在这里添加选择文本的预处理逻辑
    }
  });
  
  // 页面加载完成后的初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePage);
  } else {
    initializePage();
  }
  
  function initializePage() {
    // 可以在这里添加页面初始化逻辑
    // 比如检测页面类型，为智能分类做准备
    const pageType = detectPageType();
    if (pageType) {
      // 向background发送页面类型信息
      chrome.runtime.sendMessage({
        action: 'updatePageType',
        pageType: pageType
      });
    }
  }
  
  // 检测页面类型
  function detectPageType() {
    const url = window.location.href;
    const title = document.title;
    const meta = document.querySelector('meta[name="description"]');
    const description = meta ? meta.content : '';

    // 基于URL和内容的简单页面类型检测
    if (url.includes('github.com')) return 'github';
    if (url.includes('stackoverflow.com')) return 'stackoverflow';
    if (url.includes('medium.com') || url.includes('dev.to')) return 'blog';
    if (document.querySelector('article')) return 'article';
    if (title.includes('documentation') || title.includes('docs')) return 'documentation';

    return null;
  }

  // ==================== 抽屉式侧边栏功能 ====================

  // 全局变量
  let drawerInjected = false;
  let drawerVisible = false;

  // 强制样式修正函数
  function forceStyleCorrection(container) {
    if (!container) return;

    // 检查容器尺寸
    const rect = container.getBoundingClientRect();
    console.log('样式修正检查:', { width: rect.width, height: rect.height });

    if (rect.width < 300) {
      console.warn('检测到宽度异常，执行强制修正...');

      // 重新应用容器样式
      container.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        right: -400px !important;
        width: 400px !important;
        min-width: 400px !important;
        max-width: 400px !important;
        height: 100vh !important;
        z-index: 2147483647 !important;
        background: white !important;
        border-left: 1px solid #e0e0e0 !important;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
        display: block !important;
        visibility: visible !important;
        box-sizing: border-box !important;
        margin: 0 !important;
        padding: 0 !important;
        transform: translateZ(0) !important;
        transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      `;

      // 重新应用内部元素样式
      const drawer = container.querySelector('.blinko-drawer');
      if (drawer) {
        drawer.style.cssText = `
          position: relative !important;
          width: 400px !important;
          min-width: 400px !important;
          max-width: 400px !important;
          height: 100vh !important;
          background: #ffffff !important;
          display: flex !important;
          flex-direction: column !important;
          overflow: hidden !important;
          margin: 0 !important;
          padding: 0 !important;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          font-size: 14px !important;
          color: #333 !important;
          box-sizing: border-box !important;
          flex-shrink: 0 !important;
        `;
      }
    }
  }

  // 为子元素应用内联样式
  function applyInlineStylesToChildren(container) {
    // 头部样式
    const header = container.querySelector('.drawer-header');
    if (header) {
      header.style.cssText = `
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding: 16px 20px !important;
        background: #f8f9fa !important;
        border-bottom: 1px solid #e0e0e0 !important;
        flex-shrink: 0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
        margin: 0 !important;
      `;
    }

    // 内容区域样式
    const contentForm = container.querySelector('.content-form');
    if (contentForm) {
      contentForm.style.cssText = `
        flex: 1 !important;
        padding: 20px !important;
        overflow-y: auto !important;
        box-sizing: border-box !important;
        margin: 0 !important;
      `;
    }

    // 按钮区域样式
    const actionButtons = container.querySelector('.action-buttons');
    if (actionButtons) {
      actionButtons.style.cssText = `
        padding: 16px 20px !important;
        border-top: 1px solid #e0e0e0 !important;
        background: #f8f9fa !important;
        flex-shrink: 0 !important;
        box-sizing: border-box !important;
        margin: 0 !important;
      `;
    }
  }

  // 清理已存在的抽屉
  function cleanupExistingDrawer() {
    const existingContainer = document.getElementById('blinko-drawer-container');
    const existingStyles = document.getElementById('blinko-drawer-styles');

    if (existingContainer) {
      existingContainer.remove();
    }
    if (existingStyles) {
      existingStyles.remove();
    }

    drawerInjected = false;
    drawerVisible = false;
  }

  // 页面加载时清理可能存在的抽屉
  cleanupExistingDrawer();

  // 注入抽屉 - 简化测试版本，用于诊断问题
  async function injectDrawer() {
    if (drawerInjected) return;

    try {
      console.log('🔧 开始注入简化测试抽屉...');

      // 创建最简单的测试容器
      const drawerContainer = document.createElement('div');
      drawerContainer.id = 'blinko-drawer-container';

      // 使用最基本的样式，添加明显的调试指示器
      drawerContainer.style.cssText = `
        position: fixed;
        top: 0;
        right: -400px;
        width: 400px;
        height: 100vh;
        z-index: 999999;
        background: red;
        border: 5px solid blue;
        transition: right 0.3s ease;
      `;

      // 创建简单的测试内容
      drawerContainer.innerHTML = `
        <div style="
          width: 100%;
          height: 100%;
          background: yellow;
          padding: 20px;
          box-sizing: border-box;
          font-size: 16px;
          color: black;
        ">
          <h2>测试抽屉</h2>
          <p>如果你能看到这个黄色区域，说明抽屉基本功能正常</p>
          <p>宽度: 400px</p>
          <p>平台: ${navigator.platform}</p>
          <p>用户代理: ${navigator.userAgent.substring(0, 50)}...</p>
          <button onclick="this.parentElement.parentElement.style.right = '-400px'" style="
            padding: 10px;
            background: green;
            color: white;
            border: none;
            cursor: pointer;
          ">关闭测试</button>
        </div>
      `;

      document.body.appendChild(drawerContainer);

      console.log('✅ 简化测试抽屉注入完成');
      console.log('容器元素:', drawerContainer);
      console.log('容器位置:', drawerContainer.getBoundingClientRect());

      drawerInjected = true;

      // 测试版本不需要加载额外的JavaScript
      console.log('✅ 简化测试抽屉注入完成，跳过JavaScript加载');

      drawerInjected = true;

      // 简化版本不需要复杂的调试代码
      console.log('✅ 测试抽屉创建完成，等待显示测试');

      // 监听窗口大小变化
      const resizeHandler = () => {
        const newHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);
        drawerContainer.style.height = `${newHeight}px`;
      };
      window.addEventListener('resize', resizeHandler);

      // 设置页面信息
      setTimeout(() => {
        sendPageInfoToDrawer();
      }, 100);

    } catch (error) {
      console.error('注入抽屉失败:', error);
    }
  }

  // 发送页面信息到抽屉
  function sendPageInfoToDrawer() {
    const container = document.getElementById('blinko-drawer-container');
    if (container) {
      const iframe = container.querySelector('iframe');
      if (iframe) {
        iframe.contentWindow.postMessage({
          action: 'updatePageInfo',
          pageInfo: {
            title: document.title,
            url: window.location.href
          }
        }, '*');
      } else {
        // 直接发送消息到抽屉窗口
        window.postMessage({
          action: 'updatePageInfo',
          pageInfo: {
            title: document.title,
            url: window.location.href
          }
        }, '*');
      }
    }
  }

  // 显示抽屉 - 简化测试版本
  async function showDrawer() {
    console.log('🔧 开始显示测试抽屉...');

    if (!drawerInjected) {
      await injectDrawer();
    }

    const container = document.getElementById('blinko-drawer-container');
    if (container) {
      console.log('✅ 找到容器，准备显示');
      console.log('显示前容器位置:', container.getBoundingClientRect());

      // 简单地移动到可见位置
      container.style.right = '0px';

      drawerVisible = true;

      // 检查显示结果
      setTimeout(() => {
        const rect = container.getBoundingClientRect();
        console.log('显示后容器位置:', rect);
        console.log('容器样式:', {
          width: container.style.width,
          height: container.style.height,
          right: container.style.right,
          position: container.style.position,
          zIndex: container.style.zIndex,
          background: container.style.background
        });

        if (rect.width > 300) {
          console.log('✅ 测试抽屉显示成功！');
        } else {
          console.error('❌ 测试抽屉显示失败，宽度异常:', rect.width);
        }
      }, 100);
    } else {
      console.error('❌ 未找到抽屉容器');
    }
  }

  // 隐藏抽屉
  function hideDrawer() {
    const container = document.getElementById('blinko-drawer-container');
    if (container) {
      container.classList.remove('open');
      drawerVisible = false;
    }
  }

  // 切换抽屉
  async function toggleDrawer() {
    if (!pageSupported) {
      console.log('⚠️ 当前页面不支持抽屉功能');
      return;
    }

    if (!drawerVisible) {
      await showDrawer();
    } else {
      hideDrawer();
    }
  }

  // 监听快捷键
  document.addEventListener('keydown', (e) => {
    // Mac系统使用Command键，其他系统使用Ctrl键
    const isModifierPressed = navigator.platform.toUpperCase().indexOf('MAC') >= 0 ? e.metaKey : e.ctrlKey;

    if (isModifierPressed && e.shiftKey && e.key === 'B') {
      e.preventDefault();
      console.log('🔧 快捷键触发，开始切换抽屉...');
      toggleDrawer();
    }
  });

  // 监听来自抽屉的消息
  window.addEventListener('message', (event) => {
    if (event.data && event.data.action) {
      switch (event.data.action) {
        case 'closeDrawer':
          hideDrawer();
          break;
        case 'getPageInfo':
          sendPageInfoToDrawer();
          break;
        case 'submitToBlinko':
          handleSubmitToBlinko(event.data.data);
          break;
      }
    }
  });

  // 处理提交到Blinko
  async function handleSubmitToBlinko(data) {
    try {
      // 发送到background script
      const response = await chrome.runtime.sendMessage({
        action: 'saveToBlinko',
        data: data
      });

      // 发送结果回抽屉
      window.postMessage({
        action: 'submitResult',
        success: response.success,
        message: response.message
      }, '*');

    } catch (error) {
      console.error('提交失败:', error);
      window.postMessage({
        action: 'submitResult',
        success: false,
        message: '提交失败: ' + error.message
      }, '*');
    }
  }

  // 不自动注入抽屉，只在用户主动调用时注入

})();