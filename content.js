// 页面内容脚本 - 处理页面级交互和快捷键反馈
(function() {
  'use strict';

  // 监听来自background的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    switch (request.action) {
      case 'getSelectedText':
        sendResponse({ text: window.getSelection().toString() });
        break;
      case 'showPageNotification':
        showPageNotification(request.message, request.type);
        break;
      case 'highlightCollectedText':
        highlightCollectedText();
        break;
      case 'toggleSidebar':
      case 'toggleDrawer':
        toggleDrawer();
        break;
      case 'showDrawer':
        showDrawer();
        break;
      case 'hideDrawer':
        hideDrawer();
        break;
    }
  });
  
  // 在页面上显示临时通知
  function showPageNotification(message, type = 'success') {
    // 移除现有通知
    const existingNotification = document.querySelector('.blinko-page-notification');
    if (existingNotification) {
      existingNotification.remove();
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'blinko-page-notification';
    notification.textContent = message;
    
    // 样式
    const styles = {
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#fff3cd',
      color: type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#856404',
      padding: '12px 20px',
      borderRadius: '8px',
      border: `1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#ffeaa7'}`,
      fontSize: '14px',
      fontWeight: '500',
      zIndex: '10000',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      maxWidth: '300px',
      wordWrap: 'break-word',
      animation: 'blinkoSlideIn 0.3s ease-out'
    };
    
    Object.assign(notification.style, styles);
    
    // 添加动画样式
    if (!document.querySelector('#blinko-notification-styles')) {
      const styleSheet = document.createElement('style');
      styleSheet.id = 'blinko-notification-styles';
      styleSheet.textContent = `
        @keyframes blinkoSlideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes blinkoSlideOut {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(styleSheet);
    }
    
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
      notification.style.animation = 'blinkoSlideOut 0.3s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }, 3000);
    
    // 点击关闭
    notification.addEventListener('click', () => {
      notification.style.animation = 'blinkoSlideOut 0.3s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    });
  }
  
  // 高亮显示刚收集的文本
  function highlightCollectedText() {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const span = document.createElement('span');
      span.className = 'blinko-collected-highlight';
      span.style.cssText = `
        background: linear-gradient(90deg, #ffeb3b, #ffc107);
        padding: 2px 4px;
        border-radius: 3px;
        animation: blinkoHighlight 2s ease-out;
      `;
      
      // 添加高亮动画
      if (!document.querySelector('#blinko-highlight-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'blinko-highlight-styles';
        styleSheet.textContent = `
          @keyframes blinkoHighlight {
            0% { background: #ffeb3b; transform: scale(1.05); }
            50% { background: #ffc107; }
            100% { background: transparent; transform: scale(1); }
          }
        `;
        document.head.appendChild(styleSheet);
      }
      
      try {
        range.surroundContents(span);
        
        // 2秒后移除高亮
        setTimeout(() => {
          const parent = span.parentNode;
          if (parent) {
            parent.replaceChild(document.createTextNode(span.textContent), span);
            parent.normalize();
          }
        }, 2000);
      } catch (e) {
        // 如果无法包围内容，则跳过高亮
      }
    }
  }
  
  // 监听键盘事件，用于快捷键反馈
  document.addEventListener('keydown', function(e) {
    // 检查是否是Blinko快捷键
    const isCtrlOrCmd = e.ctrlKey || e.metaKey;
    const isShift = e.shiftKey;
    
    if (isCtrlOrCmd && isShift) {
      switch (e.key.toLowerCase()) {
        case 's':
          e.preventDefault();
          showPageNotification('🚀 正在收集当前页面...', 'info');
          break;
        case 'a':
          e.preventDefault();
          showPageNotification('🤖 正在AI总结文章...', 'info');
          break;
        case 'c':
          e.preventDefault();
          const selectedText = window.getSelection().toString();
          if (selectedText) {
            showPageNotification('✂️ 正在收集选中文本...', 'info');
            highlightCollectedText();
          } else {
            showPageNotification('❌ 请先选中要收集的文本', 'error');
          }
          break;
      }
    }
  });
  
  // 监听选择变化，为快捷键收集做准备
  let lastSelection = '';
  document.addEventListener('selectionchange', function() {
    const currentSelection = window.getSelection().toString();
    if (currentSelection !== lastSelection) {
      lastSelection = currentSelection;
      // 可以在这里添加选择文本的预处理逻辑
    }
  });
  
  // 页面加载完成后的初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePage);
  } else {
    initializePage();
  }
  
  function initializePage() {
    // 可以在这里添加页面初始化逻辑
    // 比如检测页面类型，为智能分类做准备
    const pageType = detectPageType();
    if (pageType) {
      // 向background发送页面类型信息
      chrome.runtime.sendMessage({
        action: 'updatePageType',
        pageType: pageType
      });
    }
  }
  
  // 检测页面类型
  function detectPageType() {
    const url = window.location.href;
    const title = document.title;
    const meta = document.querySelector('meta[name="description"]');
    const description = meta ? meta.content : '';

    // 基于URL和内容的简单页面类型检测
    if (url.includes('github.com')) return 'github';
    if (url.includes('stackoverflow.com')) return 'stackoverflow';
    if (url.includes('medium.com') || url.includes('dev.to')) return 'blog';
    if (document.querySelector('article')) return 'article';
    if (title.includes('documentation') || title.includes('docs')) return 'documentation';

    return null;
  }

  // ==================== 抽屉式侧边栏功能 ====================

  // 全局变量
  let drawerInjected = false;
  let drawerVisible = false;

  // 强制样式修正函数
  function forceStyleCorrection(container) {
    if (!container) return;

    // 检查容器尺寸
    const rect = container.getBoundingClientRect();
    console.log('样式修正检查:', { width: rect.width, height: rect.height });

    if (rect.width < 300) {
      console.warn('检测到宽度异常，执行强制修正...');

      // 重新应用容器样式
      container.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        right: -400px !important;
        width: 400px !important;
        min-width: 400px !important;
        max-width: 400px !important;
        height: 100vh !important;
        z-index: 2147483647 !important;
        background: white !important;
        border-left: 1px solid #e0e0e0 !important;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
        display: block !important;
        visibility: visible !important;
        box-sizing: border-box !important;
        margin: 0 !important;
        padding: 0 !important;
        transform: translateZ(0) !important;
        transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      `;

      // 重新应用内部元素样式
      const drawer = container.querySelector('.blinko-drawer');
      if (drawer) {
        drawer.style.cssText = `
          position: relative !important;
          width: 400px !important;
          min-width: 400px !important;
          max-width: 400px !important;
          height: 100vh !important;
          background: #ffffff !important;
          display: flex !important;
          flex-direction: column !important;
          overflow: hidden !important;
          margin: 0 !important;
          padding: 0 !important;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          font-size: 14px !important;
          color: #333 !important;
          box-sizing: border-box !important;
          flex-shrink: 0 !important;
        `;
      }
    }
  }

  // 为子元素应用内联样式
  function applyInlineStylesToChildren(container) {
    // 头部样式
    const header = container.querySelector('.drawer-header');
    if (header) {
      header.style.cssText = `
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding: 16px 20px !important;
        background: #f8f9fa !important;
        border-bottom: 1px solid #e0e0e0 !important;
        flex-shrink: 0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
        margin: 0 !important;
      `;
    }

    // 内容区域样式
    const contentForm = container.querySelector('.content-form');
    if (contentForm) {
      contentForm.style.cssText = `
        flex: 1 !important;
        padding: 20px !important;
        overflow-y: auto !important;
        box-sizing: border-box !important;
        margin: 0 !important;
      `;
    }

    // 按钮区域样式
    const actionButtons = container.querySelector('.action-buttons');
    if (actionButtons) {
      actionButtons.style.cssText = `
        padding: 16px 20px !important;
        border-top: 1px solid #e0e0e0 !important;
        background: #f8f9fa !important;
        flex-shrink: 0 !important;
        box-sizing: border-box !important;
        margin: 0 !important;
      `;
    }
  }

  // 清理已存在的抽屉
  function cleanupExistingDrawer() {
    const existingContainer = document.getElementById('blinko-drawer-container');
    const existingStyles = document.getElementById('blinko-drawer-styles');

    if (existingContainer) {
      existingContainer.remove();
    }
    if (existingStyles) {
      existingStyles.remove();
    }

    drawerInjected = false;
    drawerVisible = false;
  }

  // 页面加载时清理可能存在的抽屉
  cleanupExistingDrawer();

  // 注入抽屉 - 重写版本，使用内联样式确保兼容性
  async function injectDrawer() {
    if (drawerInjected) return;

    try {
      // 获取HTML内容
      const timestamp = Date.now();
      const drawerUrl = chrome.runtime.getURL('sidebar.html') + '?v=' + timestamp;
      const response = await fetch(drawerUrl);
      const htmlContent = await response.text();

      // 创建抽屉容器，直接使用内联样式确保立即生效
      const drawerContainer = document.createElement('div');
      drawerContainer.id = 'blinko-drawer-container';

      // 强制内联样式 - 确保在所有系统上都能正确显示
      drawerContainer.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        right: -400px !important;
        width: 400px !important;
        min-width: 400px !important;
        max-width: 400px !important;
        height: 100vh !important;
        z-index: 2147483647 !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        overflow: hidden !important;
        background: white !important;
        border-left: 1px solid #e0e0e0 !important;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
        display: block !important;
        visibility: visible !important;
        box-sizing: border-box !important;
        margin: 0 !important;
        padding: 0 !important;
        transform: translateZ(0) !important;
        contain: layout style paint !important;
      `;

      // 解析HTML并注入内容
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, 'text/html');
      const drawerContent = doc.body.innerHTML;
      drawerContainer.innerHTML = drawerContent;

      // 立即为内部抽屉元素应用样式
      const drawerElement = drawerContainer.querySelector('.blinko-drawer');
      if (drawerElement) {
        drawerElement.style.cssText = `
          position: relative !important;
          width: 400px !important;
          min-width: 400px !important;
          max-width: 400px !important;
          height: 100vh !important;
          background: #ffffff !important;
          display: flex !important;
          flex-direction: column !important;
          overflow: hidden !important;
          margin: 0 !important;
          padding: 0 !important;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          font-size: 14px !important;
          color: #333 !important;
          box-sizing: border-box !important;
          flex-shrink: 0 !important;
          transform: translateZ(0) !important;
        `;
      }

      document.body.appendChild(drawerContainer);

      // 应用额外的内联样式到所有子元素
      applyInlineStylesToChildren(drawerContainer);

      // 加载JavaScript功能
      const jsUrl = chrome.runtime.getURL('sidebar.js') + '?v=' + Date.now();
      const script = document.createElement('script');
      script.src = jsUrl;
      script.onload = () => {
        console.log('Sidebar.js 加载成功');
        // 脚本加载后再次确保样式正确
        forceStyleCorrection(drawerContainer);
      };
      script.onerror = (error) => {
        console.error('Sidebar.js 加载失败:', error);
      };
      document.head.appendChild(script);

      drawerInjected = true;
      console.log('Blinko抽屉已注入（内联样式版本）');
      console.log('用户代理:', navigator.userAgent);

      // Mac兼容性检查和强制修复
      setTimeout(() => {
        const container = document.getElementById('blinko-drawer-container');
        const drawer = document.getElementById('blinkoDrawer');
        if (container && drawer) {
          const containerRect = container.getBoundingClientRect();
          const drawerRect = drawer.getBoundingClientRect();

          console.log('容器样式调试:', {
            platform: navigator.platform,
            userAgent: navigator.userAgent,
            containerRect,
            drawerRect,
            containerComputedStyle: {
              width: window.getComputedStyle(container).width,
              height: window.getComputedStyle(container).height,
              position: window.getComputedStyle(container).position,
              right: window.getComputedStyle(container).right,
              display: window.getComputedStyle(container).display
            },
            drawerComputedStyle: {
              width: window.getComputedStyle(drawer).width,
              height: window.getComputedStyle(drawer).height,
              display: window.getComputedStyle(drawer).display
            }
          });

          // Mac系统强制修复：如果宽度异常小，强制重设
          if (containerRect.width < 300 || drawerRect.width < 300) {
            console.warn('检测到Mac显示问题，执行强制修复...');

            // 强制重设容器样式
            container.style.cssText = `
              position: fixed !important;
              top: 0 !important;
              right: -400px !important;
              width: 400px !important;
              min-width: 400px !important;
              max-width: 400px !important;
              height: 100vh !important;
              z-index: 2147483647 !important;
              background: white !important;
              border-left: 1px solid #e0e0e0 !important;
              box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
              display: block !important;
              visibility: visible !important;
              transform: translateZ(0) !important;
              contain: layout style paint !important;
            `;

            // 强制重设抽屉样式
            drawer.style.cssText = `
              position: relative !important;
              width: 400px !important;
              min-width: 400px !important;
              max-width: 400px !important;
              height: 100vh !important;
              background: #ffffff !important;
              display: flex !important;
              flex-direction: column !important;
              overflow: hidden !important;
              margin: 0 !important;
              padding: 0 !important;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
              font-size: 14px !important;
              color: #333 !important;
              transform: translateZ(0) !important;
              flex-shrink: 0 !important;
            `;

            console.log('Mac强制修复完成');
          }
        }
      }, 500);

      // 监听窗口大小变化
      const resizeHandler = () => {
        const newHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);
        drawerContainer.style.height = `${newHeight}px`;
      };
      window.addEventListener('resize', resizeHandler);

      // 设置页面信息
      setTimeout(() => {
        sendPageInfoToDrawer();
      }, 100);

    } catch (error) {
      console.error('注入抽屉失败:', error);
    }
  }

  // 发送页面信息到抽屉
  function sendPageInfoToDrawer() {
    const container = document.getElementById('blinko-drawer-container');
    if (container) {
      const iframe = container.querySelector('iframe');
      if (iframe) {
        iframe.contentWindow.postMessage({
          action: 'updatePageInfo',
          pageInfo: {
            title: document.title,
            url: window.location.href
          }
        }, '*');
      } else {
        // 直接发送消息到抽屉窗口
        window.postMessage({
          action: 'updatePageInfo',
          pageInfo: {
            title: document.title,
            url: window.location.href
          }
        }, '*');
      }
    }
  }

  // 显示抽屉
  async function showDrawer() {
    if (!drawerInjected) {
      await injectDrawer();
    }

    const container = document.getElementById('blinko-drawer-container');
    if (container) {
      // 立即强制修正样式
      forceStyleCorrection(container);

      // 添加open类来触发显示动画
      container.classList.add('open');

      // 更新right位置为显示状态
      container.style.right = '0px !important';

      drawerVisible = true;

      // 发送页面信息
      setTimeout(() => {
        sendPageInfoToDrawer();
      }, 100);

      // 多次检查确保显示正确
      setTimeout(() => {
        forceStyleCorrection(container);
        const finalRect = container.getBoundingClientRect();
        console.log('显示状态检查:', {
          width: finalRect.width,
          height: finalRect.height,
          right: finalRect.right,
          visible: finalRect.width > 300,
          platform: navigator.platform
        });

        // 如果仍然有问题，最后一次强制修复
        if (finalRect.width < 300) {
          console.error('显示仍有问题，执行最终修复...');
          container.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            width: 400px !important;
            min-width: 400px !important;
            max-width: 400px !important;
            height: 100vh !important;
            z-index: 2147483647 !important;
            background: white !important;
            border-left: 1px solid #e0e0e0 !important;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
            display: block !important;
            visibility: visible !important;
            box-sizing: border-box !important;
            margin: 0 !important;
            padding: 0 !important;
          `;
        }
      }, 500);
    }
  }

  // 隐藏抽屉
  function hideDrawer() {
    const container = document.getElementById('blinko-drawer-container');
    if (container) {
      container.classList.remove('open');
      drawerVisible = false;
    }
  }

  // 切换抽屉
  async function toggleDrawer() {
    if (!drawerVisible) {
      await showDrawer();
    } else {
      hideDrawer();
    }
  }

  // 监听快捷键
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'B') {
      e.preventDefault();
      toggleDrawer();
    }
  });

  // 监听来自抽屉的消息
  window.addEventListener('message', (event) => {
    if (event.data && event.data.action) {
      switch (event.data.action) {
        case 'closeDrawer':
          hideDrawer();
          break;
        case 'getPageInfo':
          sendPageInfoToDrawer();
          break;
        case 'submitToBlinko':
          handleSubmitToBlinko(event.data.data);
          break;
      }
    }
  });

  // 处理提交到Blinko
  async function handleSubmitToBlinko(data) {
    try {
      // 发送到background script
      const response = await chrome.runtime.sendMessage({
        action: 'saveToBlinko',
        data: data
      });

      // 发送结果回抽屉
      window.postMessage({
        action: 'submitResult',
        success: response.success,
        message: response.message
      }, '*');

    } catch (error) {
      console.error('提交失败:', error);
      window.postMessage({
        action: 'submitResult',
        success: false,
        message: '提交失败: ' + error.message
      }, '*');
    }
  }

  // 不自动注入抽屉，只在用户主动调用时注入

})();