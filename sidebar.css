/* Blinko抽屉式侧边栏样式 - Mac兼容性优化 */
#blinko-drawer-container * {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

.blinko-drawer-container {
  /* 移除激进的all: initial，使用温和的重置 */
  position: fixed !important;
  top: 0 !important;
  right: -400px !important;
  width: 400px !important;
  min-width: 400px !important;
  max-width: 400px !important;
  height: 100vh !important;
  z-index: 2147483647 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
  background: white !important;
  border-left: 1px solid #e0e0e0 !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
  /* Mac WebKit 强制修复 */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  /* 强制布局保护 */
  contain: layout style paint !important;
  /* 确保显示 */
  display: block !important;
  visibility: visible !important;
}

.blinko-drawer-container.open {
  right: 0 !important;
}

.blinko-drawer {
  position: relative !important;
  width: 400px !important; /* 明确指定宽度而不是100% */
  min-width: 400px !important;
  max-width: 400px !important;
  height: 100vh !important;
  min-height: 600px !important;
  max-height: 100vh !important;
  background: #ffffff !important;
  border-left: 1px solid #e0e0e0 !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  color: #333 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  /* WebKit 特定优化 */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
}

/* Mac/WebKit 特定修复 */
@supports (-webkit-appearance: none) {
  .blinko-drawer-container {
    /* 强制硬件加速 */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
  }

  .blinko-drawer {
    /* Safari/WebKit特定样式强化 */
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* 强制宽度计算 */
    flex: 0 0 400px !important;
  }
}

/* 头部样式 */
.drawer-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 16px 20px !important;
  background: #f8f9fa !important;
  border-bottom: 1px solid #e0e0e0 !important;
  flex-shrink: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 18px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: #e0e0e0;
  color: #333;
}

/* 页面信息样式 */
.page-info {
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.info-item {
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.page-title, .page-url {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
}

.page-url {
  color: #666;
  font-size: 12px;
}

/* 内容表单样式 */
.content-form {
  flex: 1;
  min-height: 0; /* 重要：允许flex子项收缩 */
  padding: 20px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* iOS滚动优化 */
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 13px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s;
}

.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.form-group textarea::placeholder {
  color: #999;
}

/* 操作按钮样式 */
.action-buttons {
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.btn {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 16px;
}

/* 状态提示样式 */
.status-message {
  padding: 12px 20px;
  text-align: center;
  font-size: 13px;
  min-height: 20px;
}

.status-message.success {
  background: #d4edda;
  color: #155724;
  border-top: 1px solid #c3e6cb;
}

.status-message.error {
  background: #f8d7da;
  color: #721c24;
  border-top: 1px solid #f5c6cb;
}

.status-message.loading {
  background: #d1ecf1;
  color: #0c5460;
  border-top: 1px solid #bee5eb;
}

/* Mac/高DPI屏幕特定修复 */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  /* 高DPI屏幕优化 */
  .blinko-drawer-container {
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    /* 强制重绘 */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }

  .blinko-drawer {
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
    /* 确保宽度不被压缩 */
    flex-shrink: 0 !important;
    width: 400px !important;
  }

  .form-group textarea {
    -webkit-appearance: none !important;
    border-radius: 6px !important;
    /* 防止Mac上的输入框样式问题 */
    -webkit-box-sizing: border-box !important;
    box-sizing: border-box !important;
  }
}

/* 确保在所有浏览器中正确显示 */
.blinko-drawer {
  contain: layout style paint !important;
}

/* 额外的Mac Safari修复 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .blinko-drawer-container {
    /* Safari特定修复 */
    will-change: transform !important;
  }
}

/* 修复可能的滚动问题 */
.content-form::-webkit-scrollbar {
  width: 6px;
}

.content-form::-webkit-scrollbar-track {
  background: transparent;
}

.content-form::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.content-form::-webkit-scrollbar-thumb:hover {
  background: #999;
}
