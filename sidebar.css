/* 善思 blinko - 现代化侧边栏样式 */

/* 全局重置和容器样式 */
#blinko-drawer-container * {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

.blinko-drawer-container {
  position: fixed !important;
  top: 0 !important;
  right: -420px !important;
  width: 420px !important;
  min-width: 420px !important;
  max-width: 420px !important;
  height: 100vh !important;
  z-index: 2147483647 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif !important;
  transition: right 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  overflow: hidden !important;
  background: transparent !important;
  /* 硬件加速 */
  -webkit-transform: translateZ(0) !important;
  transform: translateZ(0) !important;
  contain: layout style paint !important;
  display: block !important;
  visibility: visible !important;
}

.blinko-drawer-container.open {
  right: 0 !important;
}

.blinko-drawer {
  position: relative !important;
  width: 420px !important;
  height: 100vh !important;
  background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 100%) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border-left: 1px solid rgba(229, 231, 235, 0.8) !important;
  box-shadow:
    -8px 0 32px rgba(0, 0, 0, 0.08),
    -2px 0 8px rgba(0, 0, 0, 0.04) !important;
  font-size: 14px !important;
  color: #1f2937 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

/* 头部样式 */
.drawer-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 20px 24px 16px 24px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6) !important;
  flex-shrink: 0 !important;
}

.header-left {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.brand-icon {
  width: 32px !important;
  height: 32px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.brand-info {
  display: flex !important;
  flex-direction: column !important;
  gap: 2px !important;
}

.brand-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  line-height: 1.2 !important;
}

.brand-subtitle {
  font-size: 12px !important;
  color: #6b7280 !important;
  line-height: 1.2 !important;
}

.header-actions {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.action-btn {
  width: 32px !important;
  height: 32px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(229, 231, 235, 0.8) !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  color: #6b7280 !important;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 1) !important;
  border-color: rgba(156, 163, 175, 0.8) !important;
  color: #374151 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* 主要内容区域 */
.drawer-content {
  flex: 1 !important;
  padding: 16px 24px !important;
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 16px !important;
}

/* 卡片通用样式 */
.info-card,
.content-card {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(229, 231, 235, 0.6) !important;
  border-radius: 12px !important;
  padding: 16px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

.info-card:hover,
.content-card:hover {
  border-color: rgba(156, 163, 175, 0.8) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  transform: translateY(-1px) !important;
}

/* 卡片头部 */
.card-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 12px !important;
  color: #374151 !important;
  font-size: 13px !important;
  font-weight: 500 !important;
}

.card-header > span {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.card-header svg {
  color: #6b7280 !important;
}

/* 页面信息样式 */
.page-title {
  font-size: 14px !important;
  color: #1f2937 !important;
  line-height: 1.5 !important;
  margin-bottom: 8px !important;
  word-break: break-word !important;
  font-weight: 500 !important;
}

.page-url {
  font-size: 12px !important;
  color: #6b7280 !important;
  line-height: 1.4 !important;
  word-break: break-all !important;
  background: rgba(243, 244, 246, 0.8) !important;
  padding: 6px 8px !important;
  border-radius: 6px !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
}

.copy-btn {
  position: absolute !important;
  top: 12px !important;
  right: 12px !important;
  width: 24px !important;
  height: 24px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(229, 231, 235, 0.8) !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s !important;
  color: #6b7280 !important;
}

.copy-btn:hover {
  background: rgba(255, 255, 255, 1) !important;
  color: #374151 !important;
  transform: scale(1.05) !important;
}

/* AI总结按钮 */
.ai-summary-btn {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  padding: 4px 8px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s !important;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3) !important;
}

.ai-summary-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4) !important;
}

/* 文本域样式 */
textarea {
  width: 100% !important;
  padding: 12px !important;
  border: 1px solid rgba(229, 231, 235, 0.8) !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  font-family: inherit !important;
  line-height: 1.5 !important;
  resize: vertical !important;
  transition: all 0.2s !important;
  background: rgba(255, 255, 255, 0.8) !important;
  color: #1f2937 !important;
  min-height: 80px !important;
}

textarea:focus {
  outline: none !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
}

textarea::placeholder {
  color: #9ca3af !important;
}

/* 底部操作区 */
.drawer-footer {
  padding: 20px 24px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border-top: 1px solid rgba(229, 231, 235, 0.6) !important;
  flex-shrink: 0 !important;
}

.submit-btn {
  width: 100% !important;
  padding: 14px 20px !important;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 12px !important;
  font-size: 15px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3) !important;
  position: relative !important;
  overflow: hidden !important;
}

.submit-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4) !important;
}

.submit-btn:active {
  transform: translateY(0) !important;
}

.submit-btn:disabled {
  background: #d1d5db !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.submit-icon {
  font-size: 16px !important;
}

.submit-arrow {
  transition: transform 0.2s !important;
}

.submit-btn:hover .submit-arrow {
  transform: translate(2px, -2px) !important;
}

/* 状态提示样式 */
.status-message {
  margin-top: 12px !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  font-size: 13px !important;
  text-align: center !important;
  font-weight: 500 !important;
  transition: all 0.3s !important;
  opacity: 0 !important;
  transform: translateY(10px) !important;
}

.status-message.show {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.status-message.success {
  background: rgba(16, 185, 129, 0.1) !important;
  color: #059669 !important;
  border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

.status-message.error {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #dc2626 !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.status-message.loading {
  background: rgba(59, 130, 246, 0.1) !important;
  color: #2563eb !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
}

/* 设置面板样式 */
.settings-panel {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  z-index: 1000 !important;
  transform: translateX(100%) !important;
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  display: flex !important;
  flex-direction: column !important;
}

.settings-panel.show {
  transform: translateX(0) !important;
}

.settings-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 20px 24px !important;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6) !important;
  background: rgba(248, 250, 252, 0.8) !important;
}

.settings-header h3 {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin: 0 !important;
}

.close-settings {
  width: 32px !important;
  height: 32px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(229, 231, 235, 0.8) !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  font-size: 18px !important;
  color: #6b7280 !important;
  transition: all 0.2s !important;
}

.close-settings:hover {
  background: rgba(255, 255, 255, 1) !important;
  color: #374151 !important;
}

.settings-content {
  flex: 1 !important;
  padding: 24px !important;
  overflow-y: auto !important;
}

.setting-item {
  margin-bottom: 24px !important;
}

.setting-item label {
  display: block !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 8px !important;
}

.setting-item input[type="text"],
.setting-item input[type="password"] {
  width: 100% !important;
  padding: 12px 16px !important;
  border: 1px solid rgba(229, 231, 235, 0.8) !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  background: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.2s !important;
}

.setting-item input[type="text"]:focus,
.setting-item input[type="password"]:focus {
  outline: none !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
}

/* 开关样式 */
.switch {
  position: relative !important;
  display: inline-block !important;
  width: 48px !important;
  height: 24px !important;
}

.switch input {
  opacity: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.slider {
  position: absolute !important;
  cursor: pointer !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: #d1d5db !important;
  transition: 0.3s !important;
  border-radius: 24px !important;
}

.slider:before {
  position: absolute !important;
  content: "" !important;
  height: 18px !important;
  width: 18px !important;
  left: 3px !important;
  bottom: 3px !important;
  background-color: white !important;
  transition: 0.3s !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

input:checked + .slider {
  background-color: #667eea !important;
}

input:checked + .slider:before {
  transform: translateX(24px) !important;
}

.save-settings-btn {
  width: 100% !important;
  padding: 12px 20px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s !important;
  margin-top: 16px !important;
}

.save-settings-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* 滚动条样式 */
.drawer-content::-webkit-scrollbar,
.settings-content::-webkit-scrollbar {
  width: 6px !important;
}

.drawer-content::-webkit-scrollbar-track,
.settings-content::-webkit-scrollbar-track {
  background: transparent !important;
}

.drawer-content::-webkit-scrollbar-thumb,
.settings-content::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5) !important;
  border-radius: 3px !important;
}

.drawer-content::-webkit-scrollbar-thumb:hover,
.settings-content::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7) !important;
}

/* 响应式优化 */
@media (max-height: 600px) {
  .drawer-content {
    padding: 12px 24px !important;
    gap: 12px !important;
  }

  .info-card,
  .content-card {
    padding: 12px !important;
  }

  textarea {
    min-height: 60px !important;
  }
}

/* Mac/WebKit 特定优化 */
@supports (-webkit-appearance: none) {
  .blinko-drawer-container {
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    -webkit-perspective: 1000px !important;
    perspective: 1000px !important;
  }

  .blinko-drawer {
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    flex: 0 0 420px !important;
  }

  textarea {
    -webkit-appearance: none !important;
  }
}

/* 高DPI屏幕优化 */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .blinko-drawer-container {
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
  }

  .blinko-drawer {
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
  }
}
