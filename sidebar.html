<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>善思 blinko</title>
  <link rel="stylesheet" href="sidebar.css">
</head>
<body>
  <div class="blinko-drawer" id="blinkoDrawer">
    <!-- 头部 -->
    <div class="drawer-header">
      <div class="header-left">
        <div class="brand-icon">🧠</div>
        <div class="brand-info">
          <div class="brand-title">善思 blinko</div>
          <div class="brand-subtitle">更多思考更多智慧</div>
        </div>
      </div>
      <div class="header-actions">
        <button class="action-btn settings-btn" id="settingsBtn" title="设置">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
          </svg>
        </button>
        <button class="action-btn close-btn" id="closeDrawer" title="关闭">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="drawer-content">
      <!-- 标题和链接卡片 -->
      <div class="info-card">
        <div class="card-header">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
          </svg>
          <span>标题和链接</span>
        </div>
        <div class="page-title" id="pageTitle">正在获取...</div>
        <div class="page-url" id="pageUrl">正在获取...</div>
        <button class="copy-btn" id="copyUrlBtn" title="复制链接">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
        </button>
      </div>

      <!-- 原文摘要卡片 -->
      <div class="content-card">
        <div class="card-header">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14,2 14,8 20,8"></polyline>
          </svg>
          <span>原文摘要</span>
          <button class="ai-summary-btn" id="aiSummaryBtn" title="AI总结">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
            AI总结
          </button>
        </div>
        <textarea
          id="summaryInput"
          placeholder="粘贴原文摘要..."
          rows="6"
        ></textarea>
      </div>

      <!-- 个人感想卡片 -->
      <div class="content-card">
        <div class="card-header">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 14.66V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h5.34"></path>
            <polygon points="18,2 22,6 12,16 8,16 8,12 18,2"></polygon>
          </svg>
          <span>个人感想</span>
        </div>
        <textarea
          id="thoughtsInput"
          placeholder="输入你的想法..."
          rows="4"
        ></textarea>
      </div>
    </div>

    <!-- 底部操作区 -->
    <div class="drawer-footer">
      <button class="submit-btn" id="submitBtn">
        <span class="submit-icon">🧠</span>
        <span class="submit-text">提交到 Blinko</span>
        <svg class="submit-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="7" y1="17" x2="17" y2="7"></line>
          <polyline points="7,7 17,7 17,17"></polyline>
        </svg>
      </button>

      <!-- 状态提示 -->
      <div class="status-message" id="statusMessage"></div>
    </div>

    <!-- 设置面板 -->
    <div class="settings-panel" id="settingsPanel">
      <div class="settings-header">
        <h3>设置</h3>
        <button class="close-settings" id="closeSettings">×</button>
      </div>
      <div class="settings-content">
        <div class="setting-item">
          <label>Blinko服务器地址</label>
          <input type="text" id="serverUrl" placeholder="https://your-blinko-server.com">
        </div>
        <div class="setting-item">
          <label>API密钥</label>
          <input type="password" id="apiKey" placeholder="输入API密钥">
        </div>
        <div class="setting-item">
          <label>自动获取摘要</label>
          <label class="switch">
            <input type="checkbox" id="autoSummary">
            <span class="slider"></span>
          </label>
        </div>
        <button class="save-settings-btn" id="saveSettings">保存设置</button>
      </div>
    </div>
  </div>

  <script src="sidebar.js"></script>
</body>
</html>
