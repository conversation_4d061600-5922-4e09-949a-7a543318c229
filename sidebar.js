// 善思 blinko - 现代化侧边栏脚本 v3.0
console.log('🧠 善思 blinko侧边栏脚本已加载 v3.0');

class BlinkoDrawer {
  constructor() {
    this.pageInfo = {};
    this.config = {
      serverUrl: '',
      apiKey: '',
      autoSummary: false
    };
    this.isSettingsOpen = false;
    this.init();
  }

  async init() {
    try {
      console.log('🚀 初始化善思 blinko侧边栏...');

      // 绑定事件
      this.bindEvents();

      // 获取页面信息
      await this.loadPageInfo();

      // 加载配置
      await this.loadConfig();

      // 初始化动画
      this.initAnimations();

      console.log('✅ 善思 blinko侧边栏初始化完成');
    } catch (error) {
      console.error('❌ 侧边栏初始化失败:', error);
    }
  }

  bindEvents() {
    // 关闭按钮
    const closeBtn = document.getElementById('closeDrawer');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.closeDrawer());
    }

    // 设置按钮
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => this.toggleSettings());
    }

    // 关闭设置按钮
    const closeSettings = document.getElementById('closeSettings');
    if (closeSettings) {
      closeSettings.addEventListener('click', () => this.closeSettings());
    }

    // 复制链接按钮
    const copyUrlBtn = document.getElementById('copyUrlBtn');
    if (copyUrlBtn) {
      copyUrlBtn.addEventListener('click', () => this.copyUrl());
    }

    // AI总结按钮
    const aiSummaryBtn = document.getElementById('aiSummaryBtn');
    if (aiSummaryBtn) {
      aiSummaryBtn.addEventListener('click', () => this.generateAISummary());
    }

    // 提交按钮
    const submitBtn = document.getElementById('submitBtn');
    if (submitBtn) {
      submitBtn.addEventListener('click', () => this.submitToFlomo());
    }

    // 保存设置按钮
    const saveSettings = document.getElementById('saveSettings');
    if (saveSettings) {
      saveSettings.addEventListener('click', () => this.saveSettings());
    }

    // 监听来自父页面的消息
    window.addEventListener('message', (event) => {
      if (event.data && event.data.action === 'updatePageInfo') {
        this.updatePageInfo(event.data.pageInfo);
      }
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        if (this.isSettingsOpen) {
          this.closeSettings();
        } else {
          this.closeDrawer();
        }
      }
    });
  }

  initAnimations() {
    // 为状态消息添加显示动画
    const statusMessage = document.getElementById('statusMessage');
    if (statusMessage) {
      statusMessage.classList.add('show');
    }
  }

  async loadPageInfo() {
    try {
      // 通过postMessage获取页面信息
      if (window.parent !== window) {
        window.parent.postMessage({ action: 'getPageInfo' }, '*');
      } else {
        // 直接获取页面信息
        this.updatePageInfo({
          title: document.title || '未知页面',
          url: window.location.href
        });
      }
    } catch (error) {
      console.error('获取页面信息失败:', error);
    }
  }

  async loadConfig() {
    try {
      // 从localStorage加载配置
      const savedConfig = localStorage.getItem('blinko-config');
      if (savedConfig) {
        this.config = { ...this.config, ...JSON.parse(savedConfig) };
        this.updateConfigUI();
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  }

  updatePageInfo(info) {
    this.pageInfo = info;

    const titleElement = document.getElementById('pageTitle');
    const urlElement = document.getElementById('pageUrl');

    if (titleElement) {
      titleElement.textContent = info.title;
    }

    if (urlElement) {
      urlElement.textContent = info.url;
    }
  }

  updateConfigUI() {
    const serverUrlInput = document.getElementById('serverUrl');
    const apiKeyInput = document.getElementById('apiKey');
    const autoSummaryInput = document.getElementById('autoSummary');

    if (serverUrlInput) serverUrlInput.value = this.config.serverUrl || '';
    if (apiKeyInput) apiKeyInput.value = this.config.apiKey || '';
    if (autoSummaryInput) autoSummaryInput.checked = this.config.autoSummary || false;
  }

  toggleSettings() {
    const settingsPanel = document.getElementById('settingsPanel');
    if (settingsPanel) {
      this.isSettingsOpen = !this.isSettingsOpen;
      if (this.isSettingsOpen) {
        settingsPanel.classList.add('show');
        this.updateConfigUI();
      } else {
        settingsPanel.classList.remove('show');
      }
    }
  }

  closeSettings() {
    const settingsPanel = document.getElementById('settingsPanel');
    if (settingsPanel) {
      settingsPanel.classList.remove('show');
      this.isSettingsOpen = false;
    }
  }

  async copyUrl() {
    try {
      await navigator.clipboard.writeText(this.pageInfo.url);
      this.showStatus('✅ 链接已复制', 'success');
    } catch (error) {
      console.error('复制失败:', error);
      this.showStatus('❌ 复制失败', 'error');
    }
  }

  async generateAISummary() {
    try {
      this.showStatus('🤖 AI正在生成摘要...', 'loading');

      // 这里可以集成AI摘要功能
      // 暂时使用模拟数据
      setTimeout(() => {
        const summaryInput = document.getElementById('summaryInput');
        if (summaryInput) {
          summaryInput.value = '这是AI生成的页面摘要示例。实际使用时可以集成ChatGPT或其他AI服务来自动生成页面内容摘要。';
        }
        this.showStatus('✅ AI摘要生成完成', 'success');
      }, 2000);

    } catch (error) {
      console.error('AI摘要生成失败:', error);
      this.showStatus('❌ AI摘要生成失败', 'error');
    }
  }

  saveSettings() {
    try {
      const serverUrlInput = document.getElementById('serverUrl');
      const apiKeyInput = document.getElementById('apiKey');
      const autoSummaryInput = document.getElementById('autoSummary');

      this.config = {
        serverUrl: serverUrlInput?.value || '',
        apiKey: apiKeyInput?.value || '',
        autoSummary: autoSummaryInput?.checked || false
      };

      localStorage.setItem('blinko-config', JSON.stringify(this.config));
      this.showStatus('✅ 设置已保存', 'success');

      setTimeout(() => {
        this.closeSettings();
      }, 1000);

    } catch (error) {
      console.error('保存设置失败:', error);
      this.showStatus('❌ 保存设置失败', 'error');
    }
  }

  closeDrawer() {
    // 通知父页面关闭抽屉
    if (window.parent !== window) {
      window.parent.postMessage({ action: 'closeDrawer' }, '*');
    }
  }

  async submitToFlomo() {
    try {
      // 禁用提交按钮
      const submitBtn = document.getElementById('submitBtn');
      if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
          <span class="submit-icon">⏳</span>
          <span class="submit-text">提交中...</span>
        `;
      }

      this.showStatus('🚀 正在提交到 Blinko...', 'loading');

      // 获取用户输入
      const summaryInput = document.getElementById('summaryInput');
      const thoughtsInput = document.getElementById('thoughtsInput');

      const summary = summaryInput ? summaryInput.value.trim() : '';
      const thoughts = thoughtsInput ? thoughtsInput.value.trim() : '';

      if (!summary && !thoughts) {
        this.showStatus('💡 请至少填写摘要或感想', 'error');
        this.resetSubmitButton();
        return;
      }

      // 构建提交内容
      const content = this.buildContent(summary, thoughts);

      // 通过postMessage发送到background script
      if (window.parent !== window) {
        window.parent.postMessage({
          action: 'submitToBlinko',
          data: {
            content: content,
            pageInfo: this.pageInfo,
            config: this.config
          }
        }, '*');
      }

    } catch (error) {
      console.error('提交失败:', error);
      this.showStatus('❌ 提交失败: ' + error.message, 'error');
      this.resetSubmitButton();
    }
  }

  resetSubmitButton() {
    const submitBtn = document.getElementById('submitBtn');
    if (submitBtn) {
      submitBtn.disabled = false;
      submitBtn.innerHTML = `
        <span class="submit-icon">🧠</span>
        <span class="submit-text">提交到 Blinko</span>
        <svg class="submit-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="7" y1="17" x2="17" y2="7"></line>
          <polyline points="7,7 17,7 17,17"></polyline>
        </svg>
      `;
    }
  }

  buildContent(summary, thoughts) {
    let content = '';

    // 添加页面信息
    content += `# ${this.pageInfo.title}\n\n`;
    content += `🔗 ${this.pageInfo.url}\n\n`;

    // 添加摘要
    if (summary) {
      content += `## 📄 原文摘要\n${summary}\n\n`;
    }

    // 添加感想
    if (thoughts) {
      content += `## 💭 个人感想\n${thoughts}\n\n`;
    }

    // 添加标签和时间戳
    content += `---\n`;
    content += `🏷️ #网页收集 #善思blinko\n`;
    content += `📅 ${new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })}\n`;

    return content;
  }

  showStatus(message, type = 'info') {
    const statusElement = document.getElementById('statusMessage');
    if (statusElement) {
      statusElement.textContent = message;
      statusElement.className = `status-message ${type} show`;

      // 自动清除状态
      if (type !== 'loading') {
        setTimeout(() => {
          statusElement.textContent = '';
          statusElement.className = 'status-message';
        }, 4000);
      }
    }
  }

  // 处理提交结果
  handleSubmitResult(success, message) {
    this.resetSubmitButton();

    if (success) {
      this.showStatus('🎉 ' + message, 'success');

      // 清空输入框
      const summaryInput = document.getElementById('summaryInput');
      const thoughtsInput = document.getElementById('thoughtsInput');

      if (summaryInput) {
        summaryInput.value = '';
        // 添加成功动画
        summaryInput.style.background = 'rgba(16, 185, 129, 0.1)';
        setTimeout(() => {
          summaryInput.style.background = '';
        }, 1000);
      }

      if (thoughtsInput) {
        thoughtsInput.value = '';
        thoughtsInput.style.background = 'rgba(16, 185, 129, 0.1)';
        setTimeout(() => {
          thoughtsInput.style.background = '';
        }, 1000);
      }

      // 3秒后自动关闭
      setTimeout(() => {
        this.closeDrawer();
      }, 3000);

    } else {
      this.showStatus('💥 ' + message, 'error');
    }
  }
}

// 初始化善思 blinko侧边栏
let drawer;

function initDrawer() {
  if (document.getElementById('blinkoDrawer')) {
    drawer = new BlinkoDrawer();
    console.log('🎯 善思 blinko侧边栏实例已创建');
  } else {
    // 如果元素还没有加载，等待一下再试
    setTimeout(initDrawer, 100);
  }
}

// 监听来自父页面的消息
window.addEventListener('message', (event) => {
  if (event.data && event.data.action === 'submitResult' && drawer) {
    drawer.handleSubmitResult(event.data.success, event.data.message);
  }
});

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('善思 blinko侧边栏错误:', event.error);
});

// 立即尝试初始化，或者等待DOM加载
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initDrawer);
} else {
  initDrawer();
}

// 导出给全局使用
window.BlinkoDrawer = BlinkoDrawer;
